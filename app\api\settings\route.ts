import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction } from '@/lib/transaction-utils';

// Default system settings
const defaultSettings = {
  logoUrl: '',
  companyNameAr: 'DeviceFlow',
  companyNameEn: 'DeviceFlow',
  addressAr: 'الشارع الرئيسي، المدينة، الدولة',
  addressEn: 'Main Street, City, Country',
  phone: '+************',
  email: '<EMAIL>',
  website: 'www.deviceflow.com',
  footerTextAr: 'شكرًا لتعاملكم معنا.',
  footerTextEn: 'Thank you for your business.',
  reportSettings: {
    showLogo: true,
    logoPosition: 'center',
    headerStyle: 'corporate',
    footerStyle: 'detailed',
    colorScheme: 'default',
    fontSize: 'medium',
    pageOrientation: 'portrait',
    language: 'both',
    includeTimestamp: true,
    includeWatermark: false,
    watermarkText: '',
    customColors: {
      primary: '#4299e1',
      secondary: '#2b6cb0',
      accent: '#6366f1'
    }
  }
};

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // محاولة جلب الإعدادات من قاعدة البيانات
    try {
      const settings = await prisma.systemSetting.findFirst();
      if (settings) {
        // تحويل JSON fields إذا كانت strings
        const processedSettings = {
          ...settings,
          reportSettings: settings.reportSettings ? 
            (typeof settings.reportSettings === 'string' ? 
              JSON.parse(settings.reportSettings) : 
              settings.reportSettings
            ) : defaultSettings.reportSettings
        };
        return NextResponse.json(processedSettings);
      }
    } catch (dbError) {
      console.warn('Database settings not available, using defaults:', dbError);
    }

    // إرجاع الإعدادات الافتراضية إذا لم توجد في قاعدة البيانات
    return NextResponse.json(defaultSettings);
  } catch (error) {
    console.error('Error fetching settings:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الإعدادات' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية لتحديث الإعدادات
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const data = await request.json();

    // التحقق من صحة البيانات
    if (!data || Object.keys(data).length === 0) {
      return NextResponse.json(
        { error: 'لم يتم إرسال بيانات' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // البحث عن إعدادات موجودة
      const existingSettings = await tx.systemSetting.findFirst();

      let settings;
      if (existingSettings) {
        // تحديث الإعدادات الموجودة
        settings = await tx.systemSetting.update({
          where: { id: existingSettings.id },
          data: {
            logoUrl: data.logoUrl !== undefined ? data.logoUrl : existingSettings.logoUrl,
            companyNameAr: data.companyNameAr !== undefined ? data.companyNameAr : existingSettings.companyNameAr,
            companyNameEn: data.companyNameEn !== undefined ? data.companyNameEn : existingSettings.companyNameEn,
            addressAr: data.addressAr !== undefined ? data.addressAr : existingSettings.addressAr,
            addressEn: data.addressEn !== undefined ? data.addressEn : existingSettings.addressEn,
            phone: data.phone !== undefined ? data.phone : existingSettings.phone,
            email: data.email !== undefined ? data.email : existingSettings.email,
            website: data.website !== undefined ? data.website : existingSettings.website,
            footerTextAr: data.footerTextAr !== undefined ? data.footerTextAr : existingSettings.footerTextAr,
            footerTextEn: data.footerTextEn !== undefined ? data.footerTextEn : existingSettings.footerTextEn,
            reportSettings: data.reportSettings !== undefined ? 
              JSON.stringify(data.reportSettings) : 
              existingSettings.reportSettings,
          }
        });
      } else {
        // إنشاء إعدادات جديدة
        settings = await tx.systemSetting.create({
          data: {
            logoUrl: data.logoUrl || defaultSettings.logoUrl,
            companyNameAr: data.companyNameAr || defaultSettings.companyNameAr,
            companyNameEn: data.companyNameEn || defaultSettings.companyNameEn,
            addressAr: data.addressAr || defaultSettings.addressAr,
            addressEn: data.addressEn || defaultSettings.addressEn,
            phone: data.phone || defaultSettings.phone,
            email: data.email || defaultSettings.email,
            website: data.website || defaultSettings.website,
            footerTextAr: data.footerTextAr || defaultSettings.footerTextAr,
            footerTextEn: data.footerTextEn || defaultSettings.footerTextEn,
            reportSettings: data.reportSettings ? 
              JSON.stringify(data.reportSettings) : 
              JSON.stringify(defaultSettings.reportSettings),
          }
        });
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: existingSettings ? 'UPDATE' : 'CREATE',
        details: `${existingSettings ? 'Updated' : 'Created'} system settings`
      });

      return settings;
    });

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الإعدادات بنجاح',
      data: result
    });
  } catch (error) {
    console.error('Error updating settings:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث الإعدادات' },
      { status: 500 }
    );
  }
}
