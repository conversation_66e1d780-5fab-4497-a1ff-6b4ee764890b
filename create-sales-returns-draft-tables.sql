-- إنشاء جدول المسودات للمبيعات
CREATE TABLE IF NOT EXISTS sales_order_drafts (
    id SERIAL PRIMARY KEY,
    "userId" INTEGER NOT NULL,
    "formState" TEXT NOT NULL,
    "invoiceItems" TEXT NOT NULL,
    attachments TEXT,
    "soNumber" VARCHAR(255),
    "opNumber" VARCHAR(255),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- إضافة foreign key constraint إذا كان جدول users موجود
    CONSTRAINT fk_sales_draft_user 
        FOREIGN KEY ("userId") 
        REFERENCES users(id) 
        ON DELETE CASCADE
);

-- إنشاء جدول المسودات للمرتجعات
CREATE TABLE IF NOT EXISTS returns_order_drafts (
    id SERIAL PRIMARY KEY,
    "userId" INTEGER NOT NULL,
    "formState" TEXT NOT NULL,
    "returnItems" TEXT NOT NULL,
    attachments TEXT,
    "roNumber" VARCHAR(255),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- إضافة foreign key constraint إذا كان جدول users موجود
    CONSTRAINT fk_returns_draft_user 
        FOREIGN KEY ("userId") 
        REFERENCES users(id) 
        ON DELETE CASCADE
);

-- إنشاء indexes للبحث السريع
CREATE INDEX IF NOT EXISTS idx_sales_draft_user_id ON sales_order_drafts("userId");
CREATE INDEX IF NOT EXISTS idx_sales_draft_updated_at ON sales_order_drafts("updatedAt");

CREATE INDEX IF NOT EXISTS idx_returns_draft_user_id ON returns_order_drafts("userId");
CREATE INDEX IF NOT EXISTS idx_returns_draft_updated_at ON returns_order_drafts("updatedAt");

-- إنشاء triggers لتحديث updatedAt تلقائياً
CREATE OR REPLACE FUNCTION update_sales_draft_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE OR REPLACE FUNCTION update_returns_draft_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_sales_draft_updated_at
    BEFORE UPDATE ON sales_order_drafts
    FOR EACH ROW
    EXECUTE FUNCTION update_sales_draft_updated_at();

CREATE TRIGGER update_returns_draft_updated_at
    BEFORE UPDATE ON returns_order_drafts
    FOR EACH ROW
    EXECUTE FUNCTION update_returns_draft_updated_at();
