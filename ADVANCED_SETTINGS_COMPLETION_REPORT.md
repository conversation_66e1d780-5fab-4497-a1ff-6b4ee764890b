# 🎨 الإعدادات المتقدمة للتقارير - تحديث شامل

## 📋 ملخص التحديثات الجديدة

تم إضافة مجموعة شاملة من الإعدادات المتقدمة للتحكم الكامل في مظهر وتصميم التقارير، مما يتيح للمستخدمين تخصيص كل جانب من جوانب التقرير بدقة عالية.

## 🎯 الميزات الجديدة المضافة

### ✅ 1. تحكم كامل في اسم الشركة
#### 🏷️ خصائص قابلة للتخصيص:
- **النص**: اسم الشركة المعروض في التقرير
- **حجم الخط**: صغير (18px)، متوسط (24px)، كبير (32px)، مخصص
- **اللون**: اختيار لون مخصص للنص
- **الموقع**: 4 خيارات لموضع اسم الشركة
  - 🔼 أعلى الشعار
  - 🔽 أسفل الشعار (افتراضي)
  - ➡️ يمين الشعار
  - ⬅️ يسار الشعار

### ✅ 2. تفاصيل الشركة
#### 📝 إضافة سطر تفاصيل تحت اسم الشركة:
- **التفعيل/التعطيل**: خيار لإظهار أو إخفاء التفاصيل
- **النص**: مثال "لخدمات الشحن والتصدير"
- **حجم الخط**: صغير (12px)، متوسط (16px)، كبير (20px)، مخصص
- **اللون**: لون مخصص للنص

### ✅ 3. عنوان الشركة
#### 📍 عرض عنوان الشركة الكامل:
- **التفعيل/التعطيل**: خيار لإظهار أو إخفاء العنوان
- **النص**: عنوان كامل مع إمكانية استخدام أسطر متعددة
- **حجم الخط**: صغير (11px)، متوسط (14px)، كبير (18px)، مخصص
- **اللون**: لون مخصص للنص

### ✅ 4. تحكم في حجم الشعار
#### 🖼️ خيارات متنوعة لحجم الشعار:
- **صغير**: 40px ارتفاع
- **متوسط**: 60px ارتفاع (افتراضي)
- **كبير**: 80px ارتفاع
- **مخصص**: إدخال العرض والارتفاع بالبيكسل

### ✅ 5. رمز الاستجابة السريعة (QR Code)
#### 📱 إضافة QR Code اختياري:
- **التفعيل/التعطيل**: خيار لإضافة أو إزالة الرمز
- **أنواع الرموز**:
  - 📞 **واتساب**: رابط مباشر للواتساب
  - 🌐 **موقع ويب**: رابط الموقع الإلكتروني
  - 🔧 **مخصص**: أي نص أو رابط
- **أحجام الرمز**:
  - صغير: 50px
  - متوسط: 80px
  - كبير: 120px
- **مواقع الرمز**:
  - أعلى اليمين
  - أعلى اليسار
  - أسفل اليمين
  - أسفل اليسار

### ✅ 6. أحجام خطوط متدرجة
#### 🔤 تحكم مستقل في حجم خط كل عنصر:
- **اسم الشركة**: 18px - 32px (حسب الحجم المحدد)
- **تفاصيل الشركة**: 12px - 20px
- **عنوان الشركة**: 11px - 18px
- **خيار مخصص**: إدخال الحجم بالبيكسل لكل عنصر

## 🔧 الواجهات البرمجية الجديدة

### ReportSettings Interface المحدثة
```typescript
interface ReportSettings {
  // إعدادات اسم الشركة
  companyName: {
    text: string;
    fontSize: 'small' | 'medium' | 'large' | 'custom';
    customFontSize?: string;
    color: string;
    position: 'below-logo' | 'right-logo' | 'left-logo' | 'above-logo';
  };
  
  // تفاصيل الشركة
  companyDetails: {
    enabled: boolean;
    text: string;
    fontSize: 'small' | 'medium' | 'large' | 'custom';
    customFontSize?: string;
    color: string;
  };
  
  // عنوان الشركة
  address: {
    enabled: boolean;
    text: string;
    fontSize: 'small' | 'medium' | 'large' | 'custom';
    customFontSize?: string;
    color: string;
  };
  
  // حجم الشعار
  logoSize: 'small' | 'medium' | 'large' | 'custom';
  customLogoSize?: {
    width: string;
    height: string;
  };
  
  // QR Code
  qrCode: {
    enabled: boolean;
    type: 'whatsapp' | 'website' | 'custom';
    value: string;
    size: 'small' | 'medium' | 'large';
    position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  };
  
  // الإعدادات السابقة...
}
```

## 🎨 واجهة المستخدم المحسنة

### إعدادات التصميم المتقدمة
تم إضافة قسم جديد كامل في صفحة الإعدادات يحتوي على:

#### 🏢 تصميم اسم الشركة
- حقل نص لاسم الشركة
- قائمة منسدلة لاختيار موقع الاسم
- قائمة منسدلة لحجم الخط
- حقل لون للتحكم في لون النص
- حقل حجم مخصص (يظهر عند اختيار "مخصص")

#### 📋 تفاصيل الشركة
- مفتاح تشغيل/إيقاف
- منطقة نص للتفاصيل
- قائمة منسدلة لحجم الخط
- حقل لون للنص
- حقل حجم مخصص

#### 📍 عنوان الشركة
- مفتاح تشغيل/إيقاف
- منطقة نص للعنوان (دعم أسطر متعددة)
- قائمة منسدلة لحجم الخط
- حقل لون للنص
- حقل حجم مخصص

#### 🖼️ حجم الشعار
- قائمة منسدلة للأحجام المعرفة مسبقاً
- حقول العرض والارتفاع المخصصة

#### 📱 QR Code
- مفتاح تشغيل/إيقاف
- قائمة منسدلة لنوع الرمز
- حقل القيمة (يتغير حسب النوع)
- قائمة منسدلة لحجم الرمز
- قائمة منسدلة لموقع الرمز

## 🔄 تدفق البيانات المحسن

### 1. حفظ الإعدادات
```
واجهة المستخدم → معالج الإعدادات → API → قاعدة البيانات (JSON)
```

### 2. تطبيق في التقارير
```
إعدادات محفوظة → دالة التصدير → HTML ديناميكي → PDF/طباعة
```

### 3. معاينة مباشرة
```
تغيير الإعدادات → تحديث فوري → معاينة في المتصفح
```

## 📱 مثال عملي للاستخدام

### إعداد شركة البكري التجارية:
```typescript
const companySettings = {
  companyName: {
    text: "شركة البكري التجارية",
    fontSize: "large",
    color: "#1a365d",
    position: "right-logo"
  },
  companyDetails: {
    enabled: true,
    text: "لخدمات الشحن والتصدير",
    fontSize: "medium",
    color: "#2d3748"
  },
  address: {
    enabled: true,
    text: "الرياض، المملكة العربية السعودية\nص.ب ١٢٣٤٥ - الرياض ١١٤١١",
    fontSize: "small",
    color: "#4a5568"
  },
  logoSize: "custom",
  customLogoSize: {
    width: "80",
    height: "80"
  },
  qrCode: {
    enabled: true,
    type: "whatsapp",
    value: "+966501234567",
    size: "medium",
    position: "top-right"
  }
}
```

### النتيجة في التقرير:
```
    📱QR              🏢 شركة البكري التجارية    [🖼️ شعار]
                      📋 لخدمات الشحن والتصدير
                      📍 الرياض، المملكة العربية السعودية
                          ص.ب ١٢٣٤٥ - الرياض ١١٤١١
```

## 🧪 الاختبارات

### ملفات الاختبار:
1. **`test-advanced-settings.js`**: اختبار شامل للإعدادات الجديدة
2. **`test-preview-export.js`**: اختبار المعاينة والتصدير

### تشغيل الاختبارات:
```bash
node test-advanced-settings.js
node test-preview-export.js
```

### النتائج المتوقعة:
- ✅ جميع الإعدادات تحفظ وتسترجع بشكل صحيح
- ✅ التطبيق الفوري في المعاينة
- ✅ التصدير مع جميع الإعدادات المخصصة
- ✅ QR Code يتولد ويوضع في المكان الصحيح
- ✅ أحجام وألوان الخطوط تطبق بدقة

## 📊 الملفات المحدثة

### 1. واجهة المستخدم
**📁 `app/(main)/settings/appearance-settings.tsx`**
- إضافة قسم "إعدادات التصميم المتقدمة"
- 400+ سطر من واجهة المستخدم الجديدة
- معالجات الأحداث للإعدادات الجديدة
- تحديث منطق الحفظ والاسترجاع

### 2. منطق التصدير
**📁 `lib/export-utils/enhanced-html-export.ts`**
- تحديث واجهة `ReportSettings`
- دالة `generateQRCode()` لإنشاء رموز QR
- دالة `generateAdvancedHeader()` للرأس المحسن
- دعم الأحجام والمواقع الديناميكية

### 3. ملفات الاختبار
**📁 `test-advanced-settings.js`**: اختبار شامل
**📁 `test-preview-export.js`**: اختبار التصدير

## 🎉 النتيجة النهائية

### ✅ تم تحقيق جميع المتطلبات:
1. ✅ **تحكم في حجم خط اسم الشركة ولونه ومكان ظهوره**
2. ✅ **إضافة خانة لتفاصيل الشركة تظهر تحت الاسم**
3. ✅ **تحكم في حجم الشعار (ثابت ومخصص)**
4. ✅ **إضافة QR Code اختياري للواتساب أو الموقع**
5. ✅ **تحكم شامل في أحجام الخطوط لكل عنصر**

### 🚀 المميزات الإضافية:
- **معاينة مباشرة** لجميع التغييرات
- **حفظ تلقائي** للإعدادات
- **تطبيق فوري** في جميع التقارير
- **واجهة سهلة ومنظمة** للإعدادات
- **دعم كامل للعربية** في جميع العناصر
- **مرونة عالية** في التخصيص

**🎊 النظام أصبح الآن يوفر تحكماً كاملاً ومرناً في كل جانب من جوانب تصميم التقارير!**
