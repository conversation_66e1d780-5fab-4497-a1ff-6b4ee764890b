// اختبار الإعدادات المتقدمة الجديدة للتقارير

console.log("🧪 بدء اختبار الإعدادات المتقدمة...");

// إعدادات التقارير المحسنة الجديدة
const advancedReportSettings = {
  // إعدادات اسم الشركة
  companyName: {
    text: "شركة البكري التجارية",
    fontSize: "large",
    color: "#1a365d",
    position: "right-logo"
  },
  
  // تفاصيل الشركة
  companyDetails: {
    enabled: true,
    text: "لخدمات الشحن والتصدير",
    fontSize: "medium",
    color: "#2d3748"
  },
  
  // عنوان الشركة
  address: {
    enabled: true,
    text: "الرياض، المملكة العربية السعودية\nص.ب ١٢٣٤٥ - الرياض ١١٤١١",
    fontSize: "small",
    color: "#4a5568"
  },
  
  // حجم الشعار
  logoSize: "custom",
  customLogoSize: {
    width: "80",
    height: "80"
  },
  
  // QR Code
  qrCode: {
    enabled: true,
    type: "whatsapp",
    value: "+966501234567",
    size: "medium",
    position: "top-right"
  },
  
  // إعدادات عامة
  headerStyle: "detailed",
  footerText: "جميع الحقوق محفوظة - شركة البكري التجارية ٢٠٢٥",
  showTimestamp: true,
  colorScheme: "blue",
  fontSize: "medium",
  orientation: "portrait",
  language: "both",
  
  // العلامة المائية
  watermark: {
    enabled: true,
    text: "سري ومحدود"
  }
};

// اختبار إعدادات اسم الشركة
function testCompanyNameSettings() {
  console.log("\n🏢 اختبار إعدادات اسم الشركة:");
  
  const companyNameConfig = advancedReportSettings.companyName;
  
  console.log("✅ النص:", companyNameConfig.text);
  console.log("✅ الموقع:", companyNameConfig.position);
  console.log("✅ حجم الخط:", companyNameConfig.fontSize);
  console.log("✅ اللون:", companyNameConfig.color);
  
  // اختبار مواقع مختلفة
  const positions = ['above-logo', 'below-logo', 'right-logo', 'left-logo'];
  positions.forEach(position => {
    console.log(`   - ${position}: ${position === companyNameConfig.position ? '✅ محدد' : '⭕ غير محدد'}`);
  });
}

// اختبار إعدادات تفاصيل الشركة
function testCompanyDetailsSettings() {
  console.log("\n📋 اختبار إعدادات تفاصيل الشركة:");
  
  const detailsConfig = advancedReportSettings.companyDetails;
  
  console.log("✅ مفعل:", detailsConfig.enabled ? 'نعم' : 'لا');
  console.log("✅ النص:", detailsConfig.text);
  console.log("✅ حجم الخط:", detailsConfig.fontSize);
  console.log("✅ اللون:", detailsConfig.color);
}

// اختبار إعدادات العنوان
function testAddressSettings() {
  console.log("\n📍 اختبار إعدادات العنوان:");
  
  const addressConfig = advancedReportSettings.address;
  
  console.log("✅ مفعل:", addressConfig.enabled ? 'نعم' : 'لا');
  console.log("✅ النص:", addressConfig.text);
  console.log("✅ حجم الخط:", addressConfig.fontSize);
  console.log("✅ اللون:", addressConfig.color);
}

// اختبار إعدادات حجم الشعار
function testLogoSizeSettings() {
  console.log("\n🖼️ اختبار إعدادات حجم الشعار:");
  
  const logoSize = advancedReportSettings.logoSize;
  const customSize = advancedReportSettings.customLogoSize;
  
  console.log("✅ حجم الشعار:", logoSize);
  
  if (logoSize === 'custom' && customSize) {
    console.log("✅ العرض المخصص:", customSize.width + "px");
    console.log("✅ الارتفاع المخصص:", customSize.height + "px");
  }
  
  // اختبار أحجام مختلفة
  const sizes = {
    small: '40px',
    medium: '60px', 
    large: '80px',
    custom: customSize ? `${customSize.width}x${customSize.height}px` : 'غير محدد'
  };
  
  Object.entries(sizes).forEach(([size, dimension]) => {
    console.log(`   - ${size}: ${dimension} ${size === logoSize ? '✅' : '⭕'}`);
  });
}

// اختبار إعدادات QR Code
function testQRCodeSettings() {
  console.log("\n📱 اختبار إعدادات QR Code:");
  
  const qrConfig = advancedReportSettings.qrCode;
  
  console.log("✅ مفعل:", qrConfig.enabled ? 'نعم' : 'لا');
  
  if (qrConfig.enabled) {
    console.log("✅ النوع:", qrConfig.type);
    console.log("✅ القيمة:", qrConfig.value);
    console.log("✅ الحجم:", qrConfig.size);
    console.log("✅ الموقع:", qrConfig.position);
    
    // اختبار URL المولد
    let generatedURL = '';
    switch (qrConfig.type) {
      case 'whatsapp':
        generatedURL = `https://wa.me/${qrConfig.value.replace(/[^\d]/g, '')}`;
        break;
      case 'website':
        generatedURL = qrConfig.value;
        break;
      default:
        generatedURL = qrConfig.value;
    }
    
    console.log("✅ الرابط المولد:", generatedURL);
  }
}

// اختبار أحجام الخطوط المختلفة
function testFontSizeSettings() {
  console.log("\n🔤 اختبار أحجام الخطوط:");
  
  const fontSizeMap = {
    small: { companyName: '18px', details: '12px', address: '11px' },
    medium: { companyName: '24px', details: '16px', address: '14px' },
    large: { companyName: '32px', details: '20px', address: '18px' }
  };
  
  console.log("📏 اسم الشركة:", advancedReportSettings.companyName.fontSize);
  console.log("📏 تفاصيل الشركة:", advancedReportSettings.companyDetails.fontSize);
  console.log("📏 العنوان:", advancedReportSettings.address.fontSize);
  
  // عرض الأحجام بالبيكسل
  Object.entries(fontSizeMap).forEach(([size, dimensions]) => {
    console.log(`   ${size}:`);
    console.log(`     - اسم الشركة: ${dimensions.companyName}`);
    console.log(`     - التفاصيل: ${dimensions.details}`);
    console.log(`     - العنوان: ${dimensions.address}`);
  });
}

// اختبار تكوين HTML
function testHTMLGeneration() {
  console.log("\n🌐 اختبار تولید HTML:");
  
  // محاكاة إنشاء QR Code
  function mockQRCodeGeneration(value, type) {
    const patterns = {
      whatsapp: "██ ██ ██\n   ██   \n██    ██",
      website: "██    ██\n ██  ██ \n██ ██ ██",
      custom: "██ ██   \n   ██ ██\n██    ██"
    };
    
    return `data:image/svg+xml;base64,${btoa(`<svg>${patterns[type] || patterns.custom}</svg>`)}`;
  }
  
  // اختبار تطبيق الإعدادات
  const testHTML = `
    <!-- رأس الصفحة المحسن -->
    <div class="report-header" style="position: relative;">
      
      <!-- QR Code -->
      ${advancedReportSettings.qrCode.enabled ? `
        <div class="qr-code" style="position: absolute; ${advancedReportSettings.qrCode.position};">
          <img src="${mockQRCodeGeneration(advancedReportSettings.qrCode.value, advancedReportSettings.qrCode.type)}" 
               alt="QR Code" 
               style="width: ${advancedReportSettings.qrCode.size === 'small' ? '50px' : advancedReportSettings.qrCode.size === 'large' ? '120px' : '80px'};" />
        </div>
      ` : ''}
      
      <!-- محتوى الرأس -->
      <div class="header-main">
        <!-- اسم الشركة -->
        <div class="company-name" style="
          font-size: ${advancedReportSettings.companyName.fontSize === 'large' ? '32px' : '24px'};
          color: ${advancedReportSettings.companyName.color};
        ">
          ${advancedReportSettings.companyName.text}
        </div>
        
        <!-- تفاصيل الشركة -->
        ${advancedReportSettings.companyDetails.enabled ? `
          <div class="company-details" style="
            font-size: ${advancedReportSettings.companyDetails.fontSize === 'medium' ? '16px' : '12px'};
            color: ${advancedReportSettings.companyDetails.color};
          ">
            ${advancedReportSettings.companyDetails.text}
          </div>
        ` : ''}
        
        <!-- العنوان -->
        ${advancedReportSettings.address.enabled ? `
          <div class="company-address" style="
            font-size: ${advancedReportSettings.address.fontSize === 'small' ? '11px' : '14px'};
            color: ${advancedReportSettings.address.color};
          ">
            ${advancedReportSettings.address.text}
          </div>
        ` : ''}
      </div>
    </div>
  `;
  
  console.log("✅ تم إنشاء HTML بنجاح");
  console.log("📊 طول HTML:", testHTML.length, "حرف");
  
  // فحص وجود العناصر
  const elementChecks = {
    "QR Code": testHTML.includes('qr-code'),
    "اسم الشركة": testHTML.includes(advancedReportSettings.companyName.text),
    "تفاصيل الشركة": testHTML.includes(advancedReportSettings.companyDetails.text),
    "العنوان": testHTML.includes(advancedReportSettings.address.text),
    "ألوان مخصصة": testHTML.includes(advancedReportSettings.companyName.color)
  };
  
  console.log("🔍 فحص العناصر:");
  Object.entries(elementChecks).forEach(([element, exists]) => {
    console.log(`  ${exists ? '✅' : '❌'} ${element}`);
  });
  
  return testHTML;
}

// تشغيل جميع الاختبارات
async function runAdvancedTests() {
  console.log("🚀 بدء الاختبارات المتقدمة...\n");
  
  try {
    // اختبار كل قسم
    testCompanyNameSettings();
    testCompanyDetailsSettings();
    testAddressSettings();
    testLogoSizeSettings();
    testQRCodeSettings();
    testFontSizeSettings();
    
    // اختبار تولید HTML
    const generatedHTML = testHTMLGeneration();
    
    console.log("\n🎉 اكتملت جميع الاختبارات بنجاح!");
    
    // ملخص الميزات الجديدة
    console.log("\n📋 الميزات الجديدة المضافة:");
    console.log("  ✅ تحكم كامل في اسم الشركة (الموقع، الحجم، اللون)");
    console.log("  ✅ إضافة تفاصيل الشركة تحت الاسم");
    console.log("  ✅ إضافة عنوان الشركة مع تحكم كامل");
    console.log("  ✅ تحكم في حجم الشعار (أحجام ثابتة + مخصص)");
    console.log("  ✅ QR Code للواتساب أو الموقع مع تحكم في الموقع والحجم");
    console.log("  ✅ أحجام خطوط متدرجة للعناصر المختلفة");
    console.log("  ✅ ألوان مخصصة لكل عنصر");
    
    console.log("\n🎨 خيارات التصميم:");
    console.log("  📍 مواقع اسم الشركة: أعلى الشعار، أسفل الشعار، يمين الشعار، يسار الشعار");
    console.log("  📐 أحجام الشعار: صغير (40px)، متوسط (60px)، كبير (80px)، مخصص");
    console.log("  📱 مواقع QR Code: أعلى اليمين، أعلى اليسار، أسفل اليمين، أسفل اليسار");
    console.log("  🔤 أحجام الخطوط: صغير، متوسط، كبير، مخصص (بالبيكسل)");
    
    return true;
    
  } catch (error) {
    console.error("\n💥 خطأ في الاختبارات:", error);
    return false;
  }
}

// تشغيل الاختبارات
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAdvancedTests,
    advancedReportSettings,
    testCompanyNameSettings,
    testCompanyDetailsSettings,
    testAddressSettings,
    testLogoSizeSettings,
    testQRCodeSettings,
    testFontSizeSettings,
    testHTMLGeneration
  };
} else {
  // تشغيل في المتصفح
  runAdvancedTests();
}
